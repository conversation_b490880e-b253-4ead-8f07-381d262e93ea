// Project Analysis component for KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

// Import the ProjectAnalytics component
import { ProjectAnalytics } from './project_analytics.js';

export class ProjectAnalysisComponent {
  constructor(container) {
    this.container = container;
    this.projects = [];
    this.filteredProjects = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'SalesOrder';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'projectsDb';
    this.storeName = 'projects';
    this.settingsStoreName = 'appSettings';
    this.dateRange = {
      start: null,
      end: null
    };
    this.notificationSystem = new NotificationSystem();
    
    // Add tab management
    this.currentTab = 'projects'; // 'projects', 'analytics'
    this.analyticsComponent = null;
    this.contentArea = null;
    this.tableContainer = null;

    // Monday.com API configuration
    this.mondayApiUrl = 'https://primary-production-3be34.up.railway.app/webhook/monday';
    this.mondayAuth = {
      username: '<EMAIL>',
      password: '@Envent456'
    };
  }

  async init() {
    console.log("Initializing Project Analysis component");
    
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load settings if needed
      await this.loadSettings();
      
      // Load project data
      await this.loadData();
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize analytics component but don't show it yet
      this.analyticsComponent = new ProjectAnalytics(this.container, this);
    } catch (error) {
      console.error("Error initializing projects:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        const version = this.settingsStoreName && !db.objectStoreNames.contains(this.settingsStoreName) ? 
          Math.max(currentVersion + 1, 2) : currentVersion;
        
        const request = indexedDB.open(this.dbName, version);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open projects database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened projects database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            store.createIndex("ProjectID", "ProjectID", { unique: false });
            store.createIndex("ProjectName", "ProjectName", { unique: false });
            store.createIndex("Status", "Status", { unique: false });
            store.createIndex("ShippingStatus", "ShippingStatus", { unique: false });
            store.createIndex("LastUpdated", "LastUpdated", { unique: false });
          }
          
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          }
          
          console.log("Projects database schema upgraded to version", db.version);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(new Error("Could not check database version"));
      };
    });
  }

  async loadSettings() {
    try {
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      console.log("Successfully opened database for loading settings, version:", db.version);
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      const projectSettings = await new Promise((resolve) => {
        const request = store.get("projectSettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading project settings:", event.target.error);
          resolve(null);
        };
      });
      
      if (projectSettings) {
        console.log("Loaded project settings:", projectSettings);
      } else {
        console.log("No saved project settings, using defaults");
      }
      
      db.close();
    } catch (error) {
      console.error("Error loading settings:", error);
    }
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      
      if (forceRefresh || navigator.onLine) {
        console.log("Fetching projects from Monday.com");
        try {
          const result = await this.fetchMondayProjects();
          
          if (result.success) {
            this.projects = this.parseMondayProjects(result.data);
            await this.storeProjectsInIndexedDB(this.projects);
            console.log(`Stored ${this.projects.length} projects in IndexedDB`);
          } else {
            console.warn("Error fetching from Monday.com, trying IndexedDB:", result.error);
            this.projects = await this.getProjectsFromIndexedDB();
            
            if (this.projects.length === 0) {
              this.projects = this.generateSampleData();
            }
          }
        } catch (fetchError) {
          console.error("Error fetching projects from Monday.com:", fetchError);
          this.projects = await this.getProjectsFromIndexedDB();
          
          if (this.projects.length === 0) {
            this.projects = this.generateSampleData();
          }
        }
      } else {
        this.projects = await this.getProjectsFromIndexedDB();
        
        if (this.projects.length === 0) {
          console.log("No projects in IndexedDB, generating sample data");
          this.projects = this.generateSampleData();
        }
      }
      
      this.filteredProjects = [...this.projects];
      this.calculateTotalPages();
      
      this.isLoading = false;
    } catch (error) {
      console.error('Error loading project data:', error);
      this.projects = this.generateSampleData();
      this.filteredProjects = [...this.projects];
      this.calculateTotalPages();
      this.isLoading = false;
    }
  }

  async fetchMondayProjects() {
    try {
      console.log("Fetching projects with URL:", this.mondayApiUrl);
      
      const response = await fetch(this.mondayApiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': 'Basic ' + btoa(`${this.mondayAuth.username}:${this.mondayAuth.password}`)
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please check Monday.com credentials.');
        }
        throw new Error(`Failed to fetch projects: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching projects from Monday.com:", error);
      return { success: false, error: error.message };
    }
  }

  parseMondayProjects(projectsData) {
    try {
      if (!Array.isArray(projectsData)) {
        console.warn("Projects data is not an array:", projectsData);
        return [];
      }

      return projectsData.map((project, index) => {
        const parseAndNormalizeDate = (dateString) => {
          if (!dateString || typeof dateString !== 'string') return null;
          
          try {
            if (dateString.includes('UTC')) {
              return new Date(dateString);
            }
            
            const datePart = dateString.substring(0, 10);
            if (!/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
              console.warn(`Invalid date format: ${datePart} from ${dateString}`);
              return null;
            }

            const date = new Date(`${datePart}T00:00:00.000Z`);
            if (isNaN(date.getTime())) {
              console.warn(`Invalid date created: ${datePart}T00:00:00.000Z`);
              return null;
            }
            return date;
          } catch(e) {
            console.error(`Error parsing date: ${dateString}`, e);
            return null;
          }
        };

        // Extract data from the correct structure
        const id = project.id || `project-${Date.now()}-${index}`;
        const salesOrder = project.name || `SO-${1000 + index}`;
        const createdAt = parseAndNormalizeDate(project.created_at) || new Date();
        const state = project.state || 'active';
        const columns = project.columns || {};
        
        // Extract ALL main project information from columns (handle nulls properly)
        const client = columns["Client"] || '';
        const endUser = columns["End User"] || '';
        const kickoff = parseAndNormalizeDate(columns["Kickoff"]);
        const due = parseAndNormalizeDate(columns["Due"]);
        const poNumber = columns["PO#"] || '';
        const quoteNumber = columns["Quote#"] || '';
        const bomDrawing = columns["BOM & Drwg"] || '';
        const inventory = columns["Inventory"] === null ? 'Not Set' : (columns["Inventory"] || '');
        const assembly = columns["Assembly"] === null ? 'Not Set' : (columns["Assembly"] || '');
        const calibration = columns["Calibration"] === null ? 'Not Set' : (columns["Calibration"] || '');
        const qcQa = columns["QC/QA"] === null ? 'Not Set' : (columns["QC/QA"] || '');
        const productLine = columns["Product Line"] === null ? 'Not Set' : (columns["Product Line"] || '');
        const service = columns["Service"] || '';
        const specialParts = columns["Special Parts"] || '';
        const lpc = columns["LPC"] || '';
        const fat = columns["FAT"] || '';
        const specialDoc = columns["Special Doc"] || '';
        const finance = columns["Finance"] || '';
        const priority = columns["Priority"] || '';
        const shipping = columns["Shipping"] || 'Pending';
        const sales = columns["Sales"] || '';
        const pm = columns["PM"] || '';
        const timeline = columns["Timeline"] || '';
        const delayCode = columns["Delay Code"] || '';
        const actualShipDate = parseAndNormalizeDate(columns["Actual ship date"]);
        const destination = columns["Destination"] || '';
        const totalCAD = columns["Total $ CAD"] || '';
        const hubLocation = columns["Hub Location"] || '';
        const notes = columns["Notes"] || '';
        const lastUpdated = parseAndNormalizeDate(columns["Last updated"]) || new Date();
        
        // Process subitems to get ALL details
        const subitems = Array.isArray(project.subitems) ? project.subitems.map(subitem => {
          const subColumns = subitem.columns || {};
          return {
            id: subitem.id,
            name: subitem.name,
            createdAt: parseAndNormalizeDate(subitem.created_at) || new Date(),
            state: subitem.state || 'active',
            // Product Details
            product: subColumns["Product"] || '',
            serialNumber: subColumns["S/N"] || '',
            productionNumber: subColumns["Production #"] || '',
            productionDueDate: parseAndNormalizeDate(subColumns["Production Due Date"]),
            productionStartDate: parseAndNormalizeDate(subColumns["Production Start Date"]),
            // Process Stages
            inventory: subColumns["Inventory"] || '',
            assembly: subColumns["Assembly"] || '',
            assyTech: subColumns["Assy Tech"] || '',
            calTech: subColumns["Cal Tech"] || '',
            calibration: subColumns["Calibration"] || '',
            qcQa: subColumns["QC/QA"] || '',
            completionDate: parseAndNormalizeDate(subColumns["Completion Date"]),
            // Technical Specs
            power: subColumns["Power"] === null ? '' : (subColumns["Power"] || ''),
            crnAb83Testing: subColumns["CRN/AB83 Testing"] || '',
            // Staging & Shipping
            staging: subColumns["Staging"] || '',
            stagingCompleted: parseAndNormalizeDate(subColumns["Staging Completed"]),
            crated: parseAndNormalizeDate(subColumns["Crated"]) || subColumns["Crated"] || '',
            tracking: subColumns["Tracking"] || '',
            consolidation: subColumns["Consolidation"] || '',
            shippingStatus: subColumns["Shipping Status"] || '',
            shipDate: parseAndNormalizeDate(subColumns["Ship Date"]),
            // Additional Info
            description: subColumns["Description"] || '',
            lastUpdated: parseAndNormalizeDate(subColumns["Last updated"]) || new Date()
          };
        }) : [];
        
        // Determine overall status from shipping or subitems
        let overallStatus = shipping || 'Pending';
        if (subitems.length > 0) {
          // Use the most common status from subitems
          const statusCounts = {};
          subitems.forEach(item => {
            const status = item.shippingStatus || 'Pending';
            statusCounts[status] = (statusCounts[status] || 0) + 1;
          });
          const mostCommonStatus = Object.keys(statusCounts).reduce((a, b) => 
            statusCounts[a] > statusCounts[b] ? a : b, 'Pending');
          overallStatus = mostCommonStatus;
        }
        
        // Get the most recent staging completed date from subitems
        const stagingDates = subitems
          .map(item => item.stagingCompleted)
          .filter(date => date instanceof Date && !isNaN(date.getTime()));
        const mostRecentStaging = stagingDates.length > 0 
          ? new Date(Math.max(...stagingDates.map(d => d.getTime())))
          : null;
        
        // Get tracking information from subitems
        const trackingNumbers = subitems
          .map(item => item.tracking)
          .filter(tracking => tracking && tracking.trim() !== '')
          .join(', ');
        
        return {
          id,
          ProjectID: id,
          SalesOrder: salesOrder,
          CreatedAt: createdAt,
          State: state,
          Client: client,
          EndUser: endUser,
          Kickoff: kickoff,
          Due: due,
          PONumber: poNumber,
          QuoteNumber: quoteNumber,
          BOMDrawing: bomDrawing,
          Inventory: inventory,
          Assembly: assembly,
          Calibration: calibration,
          QcQa: qcQa,
          ProductLine: productLine,
          Service: service,
          SpecialParts: specialParts,
          LPC: lpc,
          FAT: fat,
          SpecialDoc: specialDoc,
          Finance: finance,
          Priority: priority,
          Sales: sales,
          PM: pm,
          Timeline: timeline,
          DelayCode: delayCode,
          ActualShipDate: actualShipDate,
          Destination: destination,
          TotalCAD: totalCAD,
          HubLocation: hubLocation,
          Notes: notes,
          StagingCompleted: mostRecentStaging,
          Created: createdAt,
          Tracking: trackingNumbers,
          Consolidation: subitems.length > 0 ? subitems[0].consolidation : '',
          ShippingStatus: overallStatus,
          ShipDate: due,
          Description: notes,
          LastUpdated: lastUpdated,
          Status: overallStatus,
          Subitems: subitems,
          SubitemCount: subitems.length,
          rawData: project
        };
      });
    } catch (error) {
      console.error("Error parsing Monday.com projects:", error);
      return [];
    }
  }

  async getProjectsFromIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open projects database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Projects store not found in database"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const projects = getAllRequest.result;
          projects.forEach(project => {
            try {
               const parseStoredDate = (dateStr) => {
                 if (!dateStr || typeof dateStr !== 'string') {
                   return null;
                 }
                 
                 if (dateStr.includes('UTC')) {
                   const date = new Date(dateStr);
                   return isNaN(date.getTime()) ? null : date;
                 }
                 
                 if (!/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                   return null;
                 }
                 
                 const date = new Date(`${dateStr}T00:00:00.000Z`);
                 return isNaN(date.getTime()) ? null : date;
               };

               project.StagingCompleted = parseStoredDate(project.StagingCompleted);
               project.Created = parseStoredDate(project.Created);
               project.ShipDate = parseStoredDate(project.ShipDate);
               project.LastUpdated = parseStoredDate(project.LastUpdated);
            } catch (dateError) {
               console.warn(`Error parsing stored dates for Project ID ${project.ProjectID}:`, dateError, project);
               project.StagingCompleted = null;
               project.Created = null;
               project.ShipDate = null;
               project.LastUpdated = null;
            }
          });
          
          console.log(`Retrieved ${projects.length} projects from IndexedDB`);
          resolve(projects);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving projects:", event.target.error);
          reject(new Error("Failed to retrieve projects from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving projects:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storeProjectsInIndexedDB(projects) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database for storing:", event.target.error);
        reject(new Error("Could not open projects database for storing"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for writing, version:", db.version);
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Projects store not found for storing"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        let count = 0;
        let errorCount = 0;
        const totalProjects = projects.length;
        
        store.clear().onsuccess = () => {
           console.log(`Cleared existing project data. Storing ${totalProjects} new projects.`);
           if (totalProjects === 0) {
               resolve();
               db.close();
               return;
           }

          projects.forEach(originalProject => {
             const projectToStore = { ...originalProject };

             try {
               const formatDateToString = (dateObj) => {
                   if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
                       return null;
                   }
                   
                   if (dateObj.toISOString) {
                     return dateObj.toISOString();
                   }
                   
                   const year = dateObj.getUTCFullYear();
                   const month = (dateObj.getUTCMonth() + 1).toString().padStart(2, '0');
                   const day = dateObj.getUTCDate().toString().padStart(2, '0');
                   return `${year}-${month}-${day}`;
               };

               projectToStore.StagingCompleted = formatDateToString(projectToStore.StagingCompleted);
               projectToStore.Created = formatDateToString(projectToStore.Created);
               projectToStore.ShipDate = formatDateToString(projectToStore.ShipDate);
               projectToStore.LastUpdated = formatDateToString(projectToStore.LastUpdated);

             } catch (conversionError) {
                 console.error(`Error converting dates to string for Project ${projectToStore.ProjectID}:`, conversionError);
                 errorCount++;
                 if (count + errorCount === totalProjects) { 
                   db.close();
                   resolve(); 
                 }
                 return;
             }
            
            const addRequest = store.add(projectToStore);
            
            addRequest.onsuccess = () => {
              count++;
              if (count + errorCount === totalProjects) {
                console.log(`Successfully stored ${count} projects.`);
                db.close();
                resolve();
              }
            };
            
            addRequest.onerror = (event) => {
              console.error("Error storing project:", projectToStore.ProjectID, event.target.error);
              errorCount++;
              if (count + errorCount === totalProjects) {
                 console.warn(`Finished storing with ${errorCount} errors.`);
                 db.close();
                 resolve();
              }
            };
          });
        };
        
        store.clear().onerror = (event) => {
          console.error("Error clearing existing project data:", event.target.error);
          db.close();
          reject(new Error("Failed to clear existing projects"));
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error storing projects:", event.target.error);
          db.close();
          reject(new Error("Failed to store projects"));
        };
      };
      
      request.onupgradeneeded = (event) => {
        console.warn("Unexpected database upgrade needed during write operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  generateSampleData() {
    const statuses = ['Shipped', 'In Transit', 'Delivered', 'Pending', 'Cancelled'];
    const clients = ['iSolve Energy', 'Acme Corp', 'TechFlow Solutions', 'Global Industries', 'DataSync Inc'];
    const salesReps = ['Alejandro Ortega', 'Thomas Robinson', 'Sarah Johnson', 'Mike Chen', 'Lisa Garcia'];
    const pms = ['Hakim Rabehi', 'Ken Henderson', 'Steve Ayodele', 'Project Manager', 'Team Lead'];
    const destinations = ['Mexico', 'Canada', 'United States', 'Europe', 'Asia'];
    const sampleData = [];
    const today = new Date();
    
    for (let i = 1; i <= 50; i++) {
      const lastModified = new Date(today);
      lastModified.setDate(today.getDate() - Math.floor(Math.random() * 180));
      
      const kickoff = new Date(lastModified);
      kickoff.setDate(lastModified.getDate() - Math.floor(Math.random() * 30));
      
      const due = new Date(kickoff);
      due.setDate(kickoff.getDate() + 30 + Math.floor(Math.random() * 60));
      
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const client = clients[Math.floor(Math.random() * clients.length)];
      const itemCount = Math.floor(Math.random() * 5) + 1;
      
      // Generate sample subitems
      const subitems = [];
      for (let j = 1; j <= itemCount; j++) {
        subitems.push({
          id: `subitem-${i}-${j}`,
          name: j.toString(),
          product: ['331SDS', 'SCS', 'TS', '4010LX', 'Enclosure'][Math.floor(Math.random() * 5)],
          shippingStatus: status,
          tracking: Math.random() > 0.7 ? `TRK${Math.floor(Math.random() * 999999999)}` : '',
          stagingCompleted: Math.random() > 0.5 ? new Date(due.getTime() - Math.random() * 86400000 * 10) : null
        });
      }
      
      sampleData.push({
        id: `${7000000000 + i}`,
        ProjectID: `${7000000000 + i}`,
        SalesOrder: `${7000 + i}`,
        Client: client,
        EndUser: Math.random() > 0.5 ? client : 'Different End User',
        Sales: salesReps[Math.floor(Math.random() * salesReps.length)],
        PM: pms[Math.floor(Math.random() * pms.length)],
        PONumber: `PO-${Math.floor(Math.random() * 9999)}`,
        QuoteNumber: Math.random() > 0.3 ? `Q-${Math.floor(Math.random() * 9999)}` : 'N/A',
        Destination: destinations[Math.floor(Math.random() * destinations.length)],
        TotalCAD: Math.random() > 0.5 ? `${(Math.random() * 500000 + 10000).toFixed(2)}` : '',
        Notes: Math.random() > 0.7 ? `Sample notes for project ${i}` : '',
        Kickoff: kickoff,
        Due: due,
        ShippingStatus: status,
        Status: status,
        LastUpdated: lastModified,
        Subitems: subitems,
        SubitemCount: itemCount,
        Tracking: subitems.map(s => s.tracking).filter(t => t).join(', ')
      });
    }
    
    return sampleData;
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredProjects.length / this.itemsPerPage));
    
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading projects...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = '';
    
    const contentElement = document.createElement('div');
    contentElement.id = 'project-content-area';
    contentElement.className = 'bg-white dark:bg-gray-800 rounded-lg shadow p-4';
    this.container.appendChild(contentElement);
    this.contentArea = contentElement;
    
    this.renderHeader();
    
    const tableContainer = document.createElement('div');
    tableContainer.id = 'project-table-container';
    contentElement.appendChild(tableContainer);
    this.tableContainer = tableContainer;
    
    this.renderActiveView();
    
    this.setupHeaderEventListeners();
  }
  
  renderHeader() {
    const headerElement = document.createElement('div');
    headerElement.className = 'project-component-header flex flex-col md:flex-row justify-between items-center mb-6'; 
    
    const styleEl = document.createElement('style');
    styleEl.textContent = `
      .project-component-header .tab-btn { 
        position: relative;
        overflow: hidden;
        transition: width 0.3s ease;
        width: 40px;
        min-width: 40px;
      }
      .project-component-header .tab-btn.active {
        width: auto;
      }
      .project-component-header .tab-btn:hover {
        width: auto;
      }
      .project-component-header .tab-label {
        opacity: 0;
        max-width: 0;
        overflow: hidden;
        white-space: nowrap;
        transition: all 0.3s ease;
        margin-left: 0;
      }
      .project-component-header .tab-btn.active .tab-label,
      .project-component-header .tab-btn:hover .tab-label {
        opacity: 1;
        max-width: 100px;
        margin-left: 6px;
      }
    `;
    document.head.appendChild(styleEl);
    
    headerElement.innerHTML = `
      <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">
        ${this.currentTab === 'projects' ? 'Project Analysis' : 'Project Analytics'}
      </h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="project-search" 
                placeholder="Search projects..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Statuses</option>
              <option value="shipped" ${this.filterStatus === 'shipped' ? 'selected' : ''}>Shipped</option>
              <option value="in transit" ${this.filterStatus === 'in transit' ? 'selected' : ''}>In Transit</option>
              <option value="delivered" ${this.filterStatus === 'delivered' ? 'selected' : ''}>Delivered</option>
              <option value="pending" ${this.filterStatus === 'pending' ? 'selected' : ''}>Pending</option>
              <option value="cancelled" ${this.filterStatus === 'cancelled' ? 'selected' : ''}>Cancelled</option>
            </select>
            
            <div class="flex gap-2">
              <!-- Project Report Icon Button -->
              <button id="project-report-button" class="tab-btn p-2 ${this.currentTab === 'projects' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Project Report">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Report</span>
              </button>
              
              <!-- Project Analytics Icon Button -->
              <button id="project-analytics-button" class="tab-btn p-2 ${this.currentTab === 'analytics' ? 'bg-blue-600 text-white active' : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'} rounded-md flex items-center" title="Project Analytics">
                <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                </svg>
                <span class="tab-label text-sm font-medium">Analytics</span>
              </button>
              
              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  <circle cx="12" cy="14" r="0.5" stroke="currentColor" stroke-width="2" />
                </svg>
              </button>
              
              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
    `;
    
    this.contentArea.appendChild(headerElement);
  }
  
  renderActiveView() {
    if (!this.tableContainer) return;
    
    this.tableContainer.innerHTML = '';
    
    if (this.currentTab === 'projects') {
      this.renderProjectsView();
    } else if (this.currentTab === 'analytics') {
      this.renderAnalyticsView();
    }
  }
  
  renderProjectsView() {
    this.tableContainer.innerHTML = `
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="SalesOrder">
                  Sales Order <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Client">
                  Client <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ShippingStatus">
                  Status <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Sales">
                  Sales Rep <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="PM">
                  Project Manager <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Due">
                  Due Date <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="TotalCAD">
                  Total CAD <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="SubitemCount">
                  Items <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="LastUpdated">
                  Last Updated <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderTableRows()}
            </tbody>
          </table>
        </div>

        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredProjects.length)} to 
            ${Math.min(this.currentPage * this.itemsPerPage, this.filteredProjects.length)} of 
            ${this.filteredProjects.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
        </div>
      </div>
    `;
    
    this.setupTableEventListeners();
  }

  renderAnalyticsView() {
    if (this.analyticsComponent) {
      const analyticsContainer = document.createElement('div');
      this.tableContainer.appendChild(analyticsContainer);
      
      this.analyticsComponent.container = analyticsContainer;
      
      if (this.dateRange.start && this.dateRange.end) {
        this.analyticsComponent.dateRange = {
          start: this.dateRange.start,
          end: this.dateRange.end
        };
      }
      
      this.analyticsComponent.filterStatus = this.filterStatus;
      this.analyticsComponent.renderHeader = false;
      
      this.analyticsComponent.init();
    } else {
      this.tableContainer.innerHTML = '<p class="text-center p-4">Analytics component not available</p>';
    }
  }

  updateButtonStates() {
    const reportButton = document.getElementById('project-report-button');
    const analyticsButton = document.getElementById('project-analytics-button');
    
    if (reportButton && analyticsButton) {
      reportButton.classList.remove('bg-blue-600', 'text-white', 'active');
      reportButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      analyticsButton.classList.remove('bg-blue-600', 'text-white', 'active');
      analyticsButton.classList.add('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
      
      if (this.currentTab === 'projects') {
        reportButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        reportButton.classList.add('bg-blue-600', 'text-white', 'active');
      } else if (this.currentTab === 'analytics') {
        analyticsButton.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'dark:bg-gray-700', 'dark:hover:bg-gray-600', 'text-gray-700', 'dark:text-gray-300');
        analyticsButton.classList.add('bg-blue-600', 'text-white', 'active');
      }
    }
  }

  switchTab(tabName) {
    if (this.currentTab === tabName) return;
    
    this.currentTab = tabName;
    
    const headerTitle = this.contentArea.querySelector('h2');
    if (headerTitle) {
      if (this.currentTab === 'projects') {
        headerTitle.textContent = 'Project Analysis';
      } else if (this.currentTab === 'analytics') {
        headerTitle.textContent = 'Project Analytics';
      }
    }
    
    this.updateButtonStates();
    this.renderActiveView();
  }

  setupHeaderEventListeners() {
    this.resetButtonEventListeners();
    
    const projectReportButton = document.getElementById('project-report-button');
    if (projectReportButton) {
      projectReportButton.addEventListener('click', () => {
        this.switchTab('projects');
      });
    }
    
    const projectAnalyticsButton = document.getElementById('project-analytics-button');
    if (projectAnalyticsButton) {
      projectAnalyticsButton.addEventListener('click', () => {
        this.switchTab('analytics');
      });
    }
    
    const searchInput = document.getElementById('project-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
      }, 300));
    }

    const clearSearchBtn = document.getElementById('clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    const statusFilter = document.getElementById('status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.filterStatus = statusFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }
    
    const refreshButton = document.getElementById('refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
      });
    }

    const exportButton = document.getElementById('export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportProjectData();
      });
    }

    const settingsButton = document.getElementById('settings-button');
    if (settingsButton) {
      settingsButton.addEventListener('click', () => {
        this.showSettings();
      });
    }

    const dateRangeButton = document.getElementById('date-range-button');
    if (dateRangeButton) {
      dateRangeButton.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }
  }
  
  resetButtonEventListeners() {
    const buttons = [
      'project-report-button',
      'project-analytics-button',
      'refresh-button',
      'export-button',
      'settings-button',
      'date-range-button'
    ];
    
    buttons.forEach(id => {
      const button = document.getElementById(id);
      if (button) {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
      }
    });
  }
  
  setupTableEventListeners() {
    if (this.currentTab !== 'projects') return;

    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    const firstPageBtn = document.getElementById('first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.renderActiveView();
        }
      });
    }

    const prevPageBtn = document.getElementById('prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.renderActiveView();
        }
      });
    }

    const nextPageBtn = document.getElementById('next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.renderActiveView();
        }
      });
    }

    const lastPageBtn = document.getElementById('last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.renderActiveView();
        }
      });
    }

    const viewButtons = document.querySelectorAll('.view-project');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const projectId = button.getAttribute('data-id');
        this.viewProject(projectId);
      });
    });

    const editButtons = document.querySelectorAll('.edit-project');
    editButtons.forEach(button => {
      button.addEventListener('click', () => {
        const projectId = button.getAttribute('data-id');
        this.editProject(projectId);
      });
    });
  }

  setupEventListeners() {
    // This method is no longer needed as we've split the event listeners
  }
  
  applyFilters() {
    let filtered = this.projects;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(project => 
        (project.SalesOrder?.toLowerCase().includes(term)) ||
        (project.Client?.toLowerCase().includes(term)) ||
        (project.EndUser?.toLowerCase().includes(term)) ||
        (project.Sales?.toLowerCase().includes(term)) ||
        (project.PM?.toLowerCase().includes(term)) ||
        (project.PONumber?.toLowerCase().includes(term)) ||
        (project.QuoteNumber?.toLowerCase().includes(term)) ||
        (project.ProductLine?.toLowerCase().includes(term)) ||
        (project.Destination?.toLowerCase().includes(term)) ||
        (project.Notes?.toLowerCase().includes(term)) ||
        (project.Tracking?.toLowerCase().includes(term)) ||
        (project.Subitems?.some(subitem => 
          subitem.product?.toLowerCase().includes(term) ||
          subitem.serialNumber?.toLowerCase().includes(term) ||
          subitem.productionNumber?.toLowerCase().includes(term)
        ))
      );
    }
    
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(project => 
        project.ShippingStatus?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);
      
      filtered = filtered.filter(project => {
        let dateToCheck = null;
        
        if (project.Kickoff instanceof Date) {
          dateToCheck = project.Kickoff;
        } else if (project.LastUpdated instanceof Date) {
          dateToCheck = project.LastUpdated;
        }
        
        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        
        return false;
      });
    }
    
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'SalesOrder':
          const idA = parseInt(valA.replace(/\D/g, '')) || 0;
          const idB = parseInt(valB.replace(/\D/g, '')) || 0;
          comparison = idA - idB;
          break;
        case 'Client':
        case 'Sales':
        case 'PM':
        case 'ShippingStatus':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'SubitemCount':
          comparison = (parseInt(valA) || 0) - (parseInt(valB) || 0);
          break;
        case 'TotalCAD':
          const cadA = parseFloat((valA || '').replace(/[^\d.-]/g, '')) || 0;
          const cadB = parseFloat((valB || '').replace(/[^\d.-]/g, '')) || 0;
          comparison = cadA - cadB;
          break;
        case 'Due':
        case 'Kickoff':
        case 'LastUpdated':
          const dateA = (valA instanceof Date && !isNaN(valA)) ? valA.getTime() : 0;
          const dateB = (valB instanceof Date && !isNaN(valB)) ? valB.getTime() : 0;
          comparison = dateA - dateB;
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredProjects = filtered;
    this.calculateTotalPages();
    
    if (this.tableContainer) {
      this.renderActiveView();
    } else {
      this.render();
    }
    
    if (this.analyticsComponent) {
      this.analyticsComponent.filterStatus = this.filterStatus;
      this.analyticsComponent.dateRange = { ...this.dateRange };
      this.analyticsComponent.calculateAnalytics().then(() => {
        if (this.currentTab === 'analytics') {
          this.analyticsComponent.render();
        }
      });
    }
  }

  showDateRangePicker() {
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);
    
    const formatDateForInput = (date) => {
      if (!date) return '';
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };
    
    const startDateValue = this.dateRange.start ? formatDateForInput(this.dateRange.start) : formatDateForInput(oneMonthAgo);
    const endDateValue = this.dateRange.end ? formatDateForInput(this.dateRange.end) : formatDateForInput(today);
    
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Select Date Range</h3>
        
        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Filter projects by their creation date. Only projects created within this date range will be displayed.
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-1">Start Date</label>
              <input 
                type="date" 
                id="date-range-start" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${startDateValue}"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium mb-1">End Date</label>
              <input 
                type="date" 
                id="date-range-end" 
                class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                value="${endDateValue}"
              >
            </div>
          </div>
        </div>
        
        <div class="flex justify-between">
          <div>
            <button id="date-range-clear" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
      </div>
    `;
    
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'date-range-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    const cancelButton = document.getElementById('date-range-cancel');
    const applyButton = document.getElementById('date-range-apply');
    const clearButton = document.getElementById('date-range-clear');
    const startDateInput = document.getElementById('date-range-start');
    const endDateInput = document.getElementById('date-range-end');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (clearButton) {
      clearButton.addEventListener('click', () => {
        this.dateRange = {
          start: null,
          end: null
        };
        
        this.currentPage = 1;
        this.applyFilters();
        
        if (this.analyticsComponent) {
          this.analyticsComponent.dateRange = { 
            start: null, 
            end: null
          };
          this.analyticsComponent.calculateAnalytics().then(() => {
            if (this.currentTab === 'analytics') {
              this.analyticsComponent.render();
            }
          });
        }
        
        this.notificationSystem.addNotification("Date filter cleared", "success");
        closeModal();
      });
    }
    
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
        
        if (startDate && endDate && startDate > endDate) {
          this.notificationSystem.addNotification("Start date cannot be after end date", "error");
          return;
        }
        
        this.dateRange = {
          start: startDate,
          end: endDate
        };
        
        this.currentPage = 1;
        this.applyFilters();
        
        if (this.analyticsComponent) {
          this.analyticsComponent.dateRange = { 
            start: startDate, 
            end: endDate
          };
          this.analyticsComponent.calculateAnalytics().then(() => {
            if (this.currentTab === 'analytics') {
              this.analyticsComponent.render();
            }
          });
        }
        
        if (startDate && endDate) {
          this.notificationSystem.addNotification(`Filtered projects from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`, "success");
        }
        closeModal();
      });
    }
    
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }

  refreshData() {
    this.loadData(true)
      .then(() => {
        this.notificationSystem.addNotification("Project data refreshed successfully", "success");
        
        if (this.analyticsComponent) {
          this.analyticsComponent.dateRange = { ...this.dateRange };
          this.analyticsComponent.filterStatus = this.filterStatus;
          this.analyticsComponent.calculateAnalytics().then(() => {
            if (this.currentTab === 'analytics') {
              this.analyticsComponent.render();
            }
          });
        }
      })
      .catch(error => {
        this.notificationSystem.addNotification("Error refreshing data: " + error.message, "error");
      });
  }

  renderTableRows() {
    if (this.filteredProjects.length === 0) {
      return `
        <tr>
          <td colspan="10" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              <p class="text-lg font-medium">No projects found</p>
              <p class="text-sm">Try adjusting your search or filter criteria</p>
            </div>
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredProjects.length);
    const displayedProjects = this.filteredProjects.slice(start, end);

    return displayedProjects.map(project => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(project.SalesOrder)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(project.Client)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(project.ShippingStatus)}">
            ${this.escapeHtml(project.ShippingStatus)}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(project.Sales || 'N/A')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(project.PM || 'N/A')}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(project.Due)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          <span class="font-semibold ${project.TotalCAD ? 'text-green-600 dark:text-green-400' : ''}">
            ${project.TotalCAD ? '$' + this.escapeHtml(project.TotalCAD) : 'N/A'}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
            ${project.SubitemCount || 0} items
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(project.LastUpdated)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${project.id}" class="view-project text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3" title="View Details">
            <i class="fas fa-eye"></i>
          </button>
          <button data-id="${project.id}" class="edit-project text-blue-600 hover:text-blue-900 dark:hover:text-blue-400" title="Edit Project">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  formatDate(date) {
    if (!(date instanceof Date) || isNaN(date.getTime())) return 'N/A';
    
    try {
      const year = date.getUTCFullYear();
      const month = date.getUTCMonth();
      const day = date.getUTCDate();
      
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      
      return `${monthNames[month]} ${day}, ${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return 'N/A';
    }
  }

  getStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'shipped':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'in transit':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'delivered':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  getProcessStatusClass(status) {
    switch (status?.toLowerCase()) {
      case 'done':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'picked':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'working on it':
      case 'assy working on it':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'not started':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'stuck':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'n/a':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-300';
      case 'not set':
        return 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  viewProject(projectId) {
    const project = this.projects.find(p => p.id === projectId);
    if (!project) return;
    
    const subitemsTable = project.Subitems && project.Subitems.length > 0 ? `
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200 border-b pb-2">Project Items (${project.Subitems.length})</h3>
        <div class="overflow-x-auto border rounded-lg">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Item</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Product</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">S/N</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Production #</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Inventory</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Assembly</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Assy Tech</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Calibration</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Cal Tech</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">QC/QA</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Completion</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Power</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Testing</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Staging</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Crated</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Tracking</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Ship Date</th>
                <th class="px-3 py-3 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${project.Subitems.map(item => `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <td class="px-3 py-3 text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(item.name)}</td>
                  <td class="px-3 py-3 text-sm text-blue-600 dark:text-blue-400 font-medium">${this.escapeHtml(item.product)}</td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.escapeHtml(item.serialNumber || '-')}</td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.escapeHtml(item.productionNumber || '-')}</td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.formatDate(item.productionDueDate)}</td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.inventory)}">
                      ${this.escapeHtml(item.inventory || 'Not Set')}
                    </span>
                  </td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.assembly)}">
                      ${this.escapeHtml(item.assembly || 'Not Set')}
                    </span>
                  </td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.escapeHtml(item.assyTech || '-')}</td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.calibration)}">
                      ${this.escapeHtml(item.calibration || 'Not Set')}
                    </span>
                  </td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.escapeHtml(item.calTech || '-')}</td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.qcQa)}">
                      ${this.escapeHtml(item.qcQa || 'Not Set')}
                    </span>
                  </td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.formatDate(item.completionDate)}</td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.escapeHtml(item.power || '-')}</td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.crnAb83Testing)}">
                      ${this.escapeHtml(item.crnAb83Testing || 'N/A')}
                    </span>
                  </td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(item.staging)}">
                      ${this.escapeHtml(item.staging || 'Not Set')}
                    </span>
                  </td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${item.crated instanceof Date ? this.formatDate(item.crated) : this.escapeHtml(item.crated || '-')}</td>
                  <td class="px-3 py-3 text-sm text-blue-600 dark:text-blue-400">${this.escapeHtml(item.tracking || '-')}</td>
                  <td class="px-3 py-3 text-sm text-gray-600 dark:text-gray-400">${this.formatDate(item.shipDate)}</td>
                  <td class="px-3 py-3">
                    <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(item.shippingStatus)}">
                      ${this.escapeHtml(item.shippingStatus || 'Pending')}
                    </span>
                  </td>
                </tr>
                ${item.description ? `
                <tr class="bg-gray-50 dark:bg-gray-800">
                  <td colspan="19" class="px-3 py-2 text-sm text-gray-600 dark:text-gray-400">
                    <strong>Description:</strong> ${this.escapeHtml(item.description)}
                  </td>
                </tr>
                ` : ''}
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    ` : '';
    
    const modalContent = `
      <div class="modal-content overflow-y-auto" style="max-height: 80vh;">
      <div class="p-6">
        <div class="flex flex-col md:flex-row justify-between mb-6">
          <h2 class="text-xl font-semibold mb-2">Sales Order ${this.escapeHtml(project.SalesOrder)}</h2>
          <div class="flex items-center">
            <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(project.ShippingStatus)}">
              ${this.escapeHtml(project.ShippingStatus)}
            </span>
          </div>
        </div>

        <!-- Project Overview Card -->
        <div class="bg-white dark:bg-gray-900 border rounded-lg p-6 mb-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Project Overview</h3>
            <div class="flex items-center space-x-2">
              <span class="px-3 py-1 text-sm font-medium rounded-full ${this.getStatusClass(project.ShippingStatus)}">
                ${this.escapeHtml(project.ShippingStatus)}
              </span>
              <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded">
                ${project.State || 'Active'}
              </span>
            </div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Core Info -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b pb-1">Core Information</h4>
              <div class="space-y-2">
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Sales Order</span>
                  <span class="text-sm font-medium text-gray-900 dark:text-white">${this.escapeHtml(project.SalesOrder)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Client</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Client)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">End User</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.EndUser || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">PO Number</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.PONumber || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Quote Number</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.QuoteNumber || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Product Line</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.ProductLine || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Priority</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Priority || '-')}</span>
                </div>
              </div>
            </div>

            <!-- Management -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b pb-1">Management</h4>
              <div class="space-y-2">
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Sales Rep</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Sales || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Project Manager</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.PM || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Timeline</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Timeline || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Kickoff Date</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.formatDate(project.Kickoff)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Due Date</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.formatDate(project.Due)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Delay Code</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.DelayCode || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Created</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.formatDate(project.CreatedAt)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Last Updated</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.formatDate(project.LastUpdated)}</span>
                </div>
              </div>
            </div>

            <!-- Progress Status -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b pb-1">Progress Status</h4>
              <div class="space-y-2">
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">BOM & Drawing</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.BOMDrawing)}">
                    ${this.escapeHtml(project.BOMDrawing || 'Not Set')}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Inventory</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.Inventory)}">
                    ${this.escapeHtml(project.Inventory || 'Not Set')}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Assembly</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.Assembly)}">
                    ${this.escapeHtml(project.Assembly || 'Not Set')}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Calibration</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.Calibration)}">
                    ${this.escapeHtml(project.Calibration || 'Not Set')}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">QC/QA</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.QcQa)}">
                    ${this.escapeHtml(project.QcQa || 'Not Set')}
                  </span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Service</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Service || '-')}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Special Parts</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.SpecialParts || '-')}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-xs text-gray-500 dark:text-gray-400">LPC</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.LPC)}">
                    ${this.escapeHtml(project.LPC || 'Not Set')}
                  </span>
                </div>
              </div>
            </div>

            <!-- Financial & Shipping -->
            <div class="space-y-3">
              <h4 class="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide border-b pb-1">Financial & Shipping</h4>
              <div class="space-y-2">
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Total CAD</span>
                  <span class="text-lg font-bold ${project.TotalCAD ? 'text-green-600 dark:text-green-400' : 'text-gray-500'}">
                    ${project.TotalCAD ? '$' + this.escapeHtml(project.TotalCAD) : 'Not Set'}
                  </span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Finance</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Finance || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Destination</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Destination || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Hub Location</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.HubLocation || '-')}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Actual Ship Date</span>
                  <span class="text-sm text-gray-700 dark:text-gray-300">${this.formatDate(project.ActualShipDate)}</span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">FAT</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.FAT)}">
                    ${this.escapeHtml(project.FAT || 'Not Set')}
                  </span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Special Doc</span>
                  <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getProcessStatusClass(project.SpecialDoc)}">
                    ${this.escapeHtml(project.SpecialDoc || 'Not Set')}
                  </span>
                </div>
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Total Items</span>
                  <span class="text-lg font-semibold text-blue-600 dark:text-blue-400">${project.SubitemCount || 0}</span>
                </div>
                ${project.Tracking ? `
                <div class="flex flex-col">
                  <span class="text-xs text-gray-500 dark:text-gray-400">Tracking Numbers</span>
                  <span class="text-sm text-blue-600 dark:text-blue-400 break-all">${this.escapeHtml(project.Tracking)}</span>
                </div>
                ` : ''}
              </div>
            </div>
          </div>
        </div>

        <!-- Notes Section -->
        ${project.Notes ? `
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
          <h4 class="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Project Notes</h4>
          <p class="text-sm text-yellow-700 dark:text-yellow-300">${this.escapeHtml(project.Notes)}</p>
        </div>
        ` : ''}

        ${project.Notes ? `
        <div class="mb-6">
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Notes</h3>
            <p class="text-sm text-gray-700 dark:text-gray-300">${this.escapeHtml(project.Notes)}</p>
          </div>
        </div>
        ` : ''}

        ${subitemsTable}

        <div class="flex justify-end gap-3">
          <button id="close-project-modal" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
        </div>
      </div>
      </div>
    `;
    
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'project-view-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-6xl w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    const closeButton = document.getElementById('close-project-modal');
    if (closeButton) {
      closeButton.addEventListener('click', closeModal);
    }
    
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        closeModal();
      }
    });
  }

  editProject(projectId) {
    this.notificationSystem.addNotification("Edit functionality coming soon", "info");
  }

  exportProjectData() {
    try {
      const dataToExport = this.filteredProjects.map(project => ({
        'Project ID': project.ProjectID,
        'Sales Order': project.SalesOrder,
        'State': project.State || '',
        'Client': project.Client,
        'End User': project.EndUser || '',
        'Status': project.ShippingStatus,
        'Sales Rep': project.Sales || '',
        'Project Manager': project.PM || '',
        'PO Number': project.PONumber || '',
        'Quote Number': project.QuoteNumber || '',
        'Product Line': project.ProductLine || '',
        'Priority': project.Priority || '',
        'BOM & Drawing': project.BOMDrawing || '',
        'Inventory': project.Inventory || '',
        'Assembly': project.Assembly || '',
        'Calibration': project.Calibration || '',
        'QC/QA': project.QcQa || '',
        'Service': project.Service || '',
        'Special Parts': project.SpecialParts || '',
        'LPC': project.LPC || '',
        'FAT': project.FAT || '',
        'Special Doc': project.SpecialDoc || '',
        'Finance': project.Finance || '',
        'Timeline': project.Timeline || '',
        'Delay Code': project.DelayCode || '',
        'Created Date': this.formatDate(project.CreatedAt),
        'Kickoff Date': this.formatDate(project.Kickoff),
        'Due Date': this.formatDate(project.Due),
        'Actual Ship Date': this.formatDate(project.ActualShipDate),
        'Destination': project.Destination || '',
        'Hub Location': project.HubLocation || '',
        'Total CAD': project.TotalCAD || '',
        'Item Count': project.SubitemCount || 0,
        'Tracking': project.Tracking || '',
        'Notes': project.Notes || '',
        'Last Updated': this.formatDate(project.LastUpdated)
      }));
      
      const csv = this.convertToCSV(dataToExport);
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `projects_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.notificationSystem.addNotification("Project data exported successfully", "success");
      }
    } catch (error) {
      console.error("Error exporting data:", error);
      this.notificationSystem.addNotification("Error exporting data: " + error.message, "error");
    }
  }

  convertToCSV(data) {
    if (data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        if (value === null || value === undefined) return '';
        const stringValue = value.toString();
        return stringValue.includes(',') ? `"${stringValue.replace(/"/g, '""')}"` : stringValue;
      }).join(',')
    );
    
    return [csvHeaders, ...csvRows].join('\n');
  }

  showSettings() {
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Project Settings</h3>
        
        <div class="mb-6">
          <label class="block text-sm font-medium mb-2">Items per page</label>
          <select id="items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
            <option value="100" ${this.itemsPerPage === 100 ? 'selected' : ''}>100</option>
          </select>
        </div>
        
        <div class="mb-6">
          <label class="block text-sm font-medium mb-2">Monday.com API Settings</label>
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium mb-1">API URL</label>
              <input type="text" id="api-url" value="${this.mondayApiUrl}" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md" readonly>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">Username</label>
              <input type="text" id="api-username" value="${this.mondayAuth.username}" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md" readonly>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end gap-3">
          <button id="cancel-settings" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="save-settings" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Settings
          </button>
        </div>
      </div>
    `;
    
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    const cancelButton = document.getElementById('cancel-settings');
    const saveButton = document.getElementById('save-settings');
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (saveButton) {
      saveButton.addEventListener('click', () => {
        const itemsPerPage = parseInt(document.getElementById('items-per-page').value);
        
        this.itemsPerPage = itemsPerPage;
        this.calculateTotalPages();
        this.applyFilters();
        
        this.notificationSystem.addNotification("Settings saved successfully", "success");
        closeModal();
      });
    }
  }

  showError(message) {
    this.notificationSystem.addNotification(message, "error");
  }
}